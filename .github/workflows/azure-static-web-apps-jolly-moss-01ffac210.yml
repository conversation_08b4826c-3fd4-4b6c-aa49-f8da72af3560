name: Azure Static Web Apps CI/CD

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened, closed]
    branches:
      - main

jobs:
  build_and_deploy_job:
    if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
    runs-on: ubuntu-latest
    name: Build and Deploy Job
    permissions:
       id-token: write
       contents: read
    steps:
      # 1) Checkout repo principal
      - uses: actions/checkout@v3
        with:
          submodules: true
          lfs: false

      # 1.5) Setup Node.js version
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.19.0'
          cache: 'npm'

      # 2) Configurar clave privada enygma-temp-microapp SSH
      - name: Setup SSH access for library
        run: |
          mkdir -p ~/.ssh
          printf "%s\n" "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/enygma-temp-microapp
          chmod 600 ~/.ssh/enygma-temp-microapp
          ssh-keyscan github.com >> ~/.ssh/known_hosts
          git config --global core.sshCommand "ssh -i ~/.ssh/enygma-temp-microapp -o StrictHostKeyChecking=no"


      # 3) Instalar dependencias necesarias
      - name: Install OIDC Client from Core Package
        run: npm install @actions/core@1.6.0 @actions/http-client

      # 4) Obtener ID Token
      - name: Get Id Token
        uses: actions/github-script@v6
        id: idtoken
        with:
           script: |
               const coredemo = require('@actions/core')
               return await coredemo.getIDToken()
           result-encoding: string

      # 5) Build y Deploy a Azure Static Web Apps
      - name: Build And Deploy
        id: builddeploy
        uses: Azure/static-web-apps-deploy@v1
        with:
          azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_JOLLY_MOSS_01FFAC210 }}
          action: "upload"
          ###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
          app_location: "/" # App source code path
          api_location: "" # Api source code path - optional
          output_location: "dist" # Built app content directory - optional
          github_id_token: ${{ steps.idtoken.outputs.result }}
          ###### End of Repository/Build Configurations ######

  close_pull_request_job:
    if: github.event_name == 'pull_request' && github.event.action == 'closed'
    runs-on: ubuntu-latest
    name: Close Pull Request Job
    steps:
      - name: Close Pull Request
        id: closepullrequest
        uses: Azure/static-web-apps-deploy@v1
        with:
          action: "close"