import { Modal } from "microapps";

interface GameExitProps {
  onConfirmExit?: () => void;
  onCancelExit?: () => void;
}

/**
 * Game Exit Component
 *
 * Displays exit confirmation screen with options to save progress or quit.
 * Features:
 * - Exit confirmation dialog
 * - Option to save current progress
 * - Return to game option
 */
export const GameExit: React.FC<GameExitProps> = ({
  onConfirmExit,
  onCancelExit
}) => {
  const handleConfirmExit = () => {
    if (onConfirmExit) {
      onConfirmExit();
    }
  };

  const handleCancelExit = () => {
    if (onCancelExit) {
      onCancelExit();
    }
  };

  return (
    <Modal
      title="¿Seguro que quieres salir del juego?"
      onClose={handleCancelExit}
      onCancel={handleConfirmExit}
      onConfirm={handleCancelExit}
      cancelText="Salir de todos modos"
      confirmText="Seguir jugando"
      body="Si sales ahora, vas a perder tu progreso actual. Puedes seguir jugando o salir cuando quieras."
    />
  );
};
