import React from "react";
import "./GameHint.scss";

interface Clue {
  id: string;
  text: string;
  sessionId: string;
  timestamp: string;
}

interface GameHintProps {
  clues?: Clue[];
}

/**
 * Game Hint Component
 *
 * Displays all the hints/clues discovered during the game.
 * Features:
 * - List of all discovered hints
 * - Chronological order
 * - Visual representation of progress
 */
export const GameHint: React.FC<GameHintProps> = ({ clues = [] }) => {

  return (
    <div className="game-hint">
      <div className="game-hint-content">
        <div className="hints-header">
          <div className="hints-icon">💡</div>
          <h1 className="hints-title">Pistas Descubiertas</h1>
          <p className="hints-subtitle">
            Has descubierto <strong>{clues.length}</strong> pistas hasta ahora
          </p>
        </div>

        {clues.length === 0 ? (
          <div className="no-hints">
            <div className="no-hints-icon">🤔</div>
            <h3>Aún no hay pistas</h3>
            <p>Haz preguntas para descubrir pistas sobre el personaje misterioso.</p>
          </div>
        ) : (
          <div className="hints-list">
            {clues.map((clue, index) => (
              <div key={clue.id} className="hint-card">
                <div className="hint-number">
                  {index + 1}
                </div>
                <div className="hint-content">
                  <div className="hint-clue">
                    <span className="clue-label">Pista:</span>
                    <span className="clue-text">{clue.text}</span>
                  </div>
                  <div className="hint-timestamp">
                    {new Date(clue.timestamp).toLocaleString('es-ES', {
                      day: '2-digit',
                      month: '2-digit',
                      year: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        <div className="hints-summary">
          <div className="summary-card">
            <h3>Resumen de Pistas</h3>
            <p>
              Usa estas pistas para hacer preguntas más específicas y acercarte
              a la respuesta correcta. Recuerda que cada pregunta cuenta,
              ¡así que piensa bien antes de preguntar!
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
