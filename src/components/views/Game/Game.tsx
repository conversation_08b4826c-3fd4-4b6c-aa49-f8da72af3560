// React core
import React, { useEffect, useRef, useState } from "react";
// Third-party library imports
import { Image } from "microapps";
// Services
import { ConversationStorage } from "../../../services/ConversationStorage";
import type { GameProgress as GameProgressType } from "../../../services/ConversationStorage";
// Components
import { SimpleVoiceChat } from "../../SimpleVoiceChat";
import { MicAdapter } from "../../MicAdapter";
// Utils & Constants & Helpers
// Hooks
import { useRealTimeConversation } from "../../../hooks/useRealTimeConversation";
// Styles
import "./Game.scss";

interface GameProps {
  generatedCharacter: string;
  gameStarted: boolean;
  isGameStarted: boolean;
  initialMessage: string;
  onGameEnd: (gameWon: boolean) => void;
  onShowGameLive: () => void;
  onShowGameHint: () => void;
  onShowGameExit: () => void;
}

/**
 * Obtiene el color de la cuenta regresiva basado en el número de preguntas restantes
 */
const getCountdownColor = (questionsRemaining: number): string => {
  if (questionsRemaining >= 16 && questionsRemaining <= 20) return "#FFFFFF";
  if (questionsRemaining >= 11 && questionsRemaining <= 15) return "#F6FFCB";
  if (questionsRemaining >= 6 && questionsRemaining <= 10) return "#EBFF88";
  if (questionsRemaining >= 1 && questionsRemaining <= 5) return "#FFD388";
  if (questionsRemaining === 0) return "#FF4548";
  return "#FFFFFF"; // Default
};

export const Game: React.FC<GameProps> = ({
  generatedCharacter,
  gameStarted,
  isGameStarted,
  initialMessage,
  onGameEnd,
  onShowGameLive,
  onShowGameHint,
  onShowGameExit,
}) => {
  // State for game progress
  const [gameProgress, setGameProgress] = useState<GameProgressType | null>(null);

  // Refs
  const conversationStorage = useRef(ConversationStorage.getInstance());

  // Hook for real-time conversation management to get mic-related data
  const {
    isActive,
    conversationState,
    error,
    startConversation,
    stopConversation,
    enableSmartMicrophone,
  } = useRealTimeConversation(
    generatedCharacter,
    isGameStarted,
    false
  );

  // Update game progress when session changes
  useEffect(() => {
    if (!isGameStarted) return;

    const updateProgress = () => {
      const progress = conversationStorage.current.getGameProgress();
      setGameProgress(progress);
    };

    updateProgress();
    const interval = setInterval(updateProgress, 1000);
    return () => clearInterval(interval);
  }, [isGameStarted]);

  // Handle voice button click - start/stop conversation
  const handleVoiceToggle = () => {
    if (isActive) {
      stopConversation();
    } else {
      startConversation().then((success) => {
        if (success) {
          enableSmartMicrophone();
        }
      });
    }
  };
  return (
    <div className="view-game">
      <div className="container">
        <div className="menu-left">
          <div className="enygma-logo">
            <Image
              src="assets/game/enygma.png"
              alt="Enygma"
              className="enygma-image"
              width="180px"
              aspectRatio="1:1"
            />

            <div className="speaking">
              <MicAdapter
                conversationState={conversationState}
                isVoiceActive={isActive}
                micLevel={0}
                onToggle={handleVoiceToggle}
                voiceError={error}
                id="game-mic"
                className="game-mic-wrapper"
              />
            </div>
          </div>
        </div>

        <div>
          <SimpleVoiceChat
            generatedCharacter={generatedCharacter}
            isGameStarted={isGameStarted}
            initialMessage={initialMessage}
            onGameEnd={onGameEnd}
          />
        </div>

        {(generatedCharacter || gameStarted) && (
          <div className="menu-right">
            <div className="game-navigation">
              <div onClick={onShowGameLive} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/lives.png"
                  alt="Vidas"
                  className="book-image"
                />
                {gameProgress && (
                  <div
                    className="countdown-display"
                    style={{
                      color: getCountdownColor(gameProgress.questionsRemaining),
                      fontSize: "24px",
                      fontWeight: "bold",
                      textAlign: "center",
                      marginTop: "8px",
                      textShadow: "2px 2px 4px rgba(0, 0, 0, 0.5)"
                    }}
                  >
                    {gameProgress.questionsRemaining}
                  </div>
                )}
              </div>

              <div onClick={onShowGameHint} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/clues.png"
                  alt="Pistas"
                  className="clues-image"
                />
                <p className="body2 bold">Pistas</p>
              </div>

              <div onClick={onShowGameExit} className="image-button">
                <Image
                  width="100%"
                  aspectRatio="1:1"
                  src="assets/game/exit.png"
                  alt="Salir"
                  className="exit-image"
                />
                <p className="body2 bold">Salir</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
