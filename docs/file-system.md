.
├── docs
│   └── file-system.md
├── .env
├── eslint.config.js
├── .github
│   └── workflows
│       └── azure-static-web-apps-jolly-moss-01ffac210.yml
├── .gitignore
├── index.html
├── package.json
├── package-lock.json
├── preamble_enygma.txt
├── public
│   ├── assets
│   │   ├── favicon.png
│   │   ├── fonts
│   │   │   ├── OnAir-BlackItalic.ttf
│   │   │   ├── OnAir-Black.ttf
│   │   │   ├── OnAir-BoldItalic.ttf
│   │   │   ├── OnAir-Bold.ttf
│   │   │   ├── OnAir-Italic.ttf
│   │   │   ├── OnAir-LightItalic.ttf
│   │   │   ├── OnAir-Light.ttf
│   │   │   ├── OnAirOutlineOne.ttf
│   │   │   ├── OnAirOutlineThree.ttf
│   │   │   ├── OnAirOutlineTwo.ttf
│   │   │   └── OnAir-Regular.ttf
│   │   ├── game
│   │   │   ├── background.png
│   │   │   ├── book_1.png
│   │   │   ├── book_2.png
│   │   │   ├── book.png
│   │   │   ├── clues.png
│   │   │   ├── enygma.png
│   │   │   ├── exit.png
│   │   │   ├── lives.png
│   │   │   └── player.png
│   │   └── sounds
│   │       ├── sound_1.mp3
│   │       ├── sound_3.mp3
│   │       └── sound.mp3
│   ├── azure-voice-conf.json
│   ├── game-modes.json
│   └── game-rules.json
├── README.md
├── src
│   ├── animations.scss
│   ├── App.scss
│   ├── App.tsx
│   ├── components
│   │   ├── CharacterInfo.tsx
│   │   ├── ConversationStateIndicator.tsx
│   │   ├── DebugPanel.scss
│   │   ├── DebugPanel.tsx
│   │   ├── GameEndScreen.tsx
│   │   ├── GameProgress.tsx
│   │   ├── HintsPopup.tsx
│   │   ├── MessageBubble.tsx
│   │   ├── MicAdapter.tsx
│   │   ├── MovistarInfo.tsx
│   │   ├── shared
│   │   │   ├── Header.scss
│   │   │   └── Header.tsx
│   │   ├── SimpleVoiceChat.tsx
│   │   └── views
│   │       ├── Cookie
│   │       │   └── Cookie.tsx
│   │       ├── Game
│   │       │   ├── Game.scss
│   │       │   └── Game.tsx
│   │       ├── GameExit
│   │       │   ├── GameExit.scss
│   │       │   └── GameExit.tsx
│   │       ├── GameHint
│   │       │   ├── GameHint.scss
│   │       │   └── GameHint.tsx
│   │       ├── GameLive
│   │       │   ├── GameLive.scss
│   │       │   └── GameLive.tsx
│   │       ├── GameRules
│   │       │   ├── GameRules.scss
│   │       │   └── GameRules.tsx
│   │       ├── MainMenu
│   │       │   ├── MainMenu.scss
│   │       │   └── MainMenu.tsx
│   │       └── Welcome
│   │           ├── Welcome.scss
│   │           └── Welcome.tsx
│   ├── hooks
│   │   └── useRealTimeConversation.ts
│   ├── index.scss
│   ├── main.tsx
│   ├── services
│   │   ├── AppService.ts
│   │   ├── AudioStateManager.ts
│   │   ├── ConversationStateMachine.ts
│   │   ├── ConversationStateManager.ts
│   │   ├── ConversationStorage.ts
│   │   ├── impl
│   │   │   ├── IAppService.ts
│   │   │   ├── IMovistarDirectService.ts
│   │   │   ├── IPerplexityService.ts
│   │   │   ├── ISpeechRecognitionService.ts
│   │   │   └── IVoicesService.ts
│   │   ├── MovistarDirectService.ts
│   │   ├── MovistarOttService.ts
│   │   ├── PerplexityService.ts
│   │   ├── SpeechRecognitionService.ts
│   │   └── VoicesService.ts
│   ├── stores
│   │   └── conversationStore.ts
│   ├── types
│   │   ├── GenerateInput.ts
│   │   ├── GenerateResponse.ts
│   │   ├── PerplexityTypes.ts
│   │   └── SelectedPresetResponse.ts
│   ├── utils
│   │   ├── apiUtils.ts
│   │   ├── audioUtils.ts
│   │   ├── conversationUtils.ts
│   │   ├── cookieUtils.ts
│   │   └── gameUtils.ts
│   └── vite-env.d.ts
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
└── vite.config.ts

27 directories, 101 files
